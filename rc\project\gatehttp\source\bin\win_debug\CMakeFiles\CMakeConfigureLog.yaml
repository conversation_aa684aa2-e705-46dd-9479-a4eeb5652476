
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:5 (project)"
    message: |
      The system is: Windows - 10.0.19045 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/cl.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35209 版
      版权所有(C) Microsoft Corporation。保留所有权利。
      
      CMakeCCompilerId.c
      Microsoft (R) Incremental Linker Version 14.44.35209.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
      
      /out:CMakeCCompilerId.exe 
      CMakeCCompilerId.obj 
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.obj"
      
      The C compiler identification is MSVC, found in:
        D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/3.31.6-msvc6/CompilerIdC/CMakeCCompilerId.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:1288 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:250 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Detecting C compiler /showIncludes prefix:
        main.c
        注意: 包含文件:  D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\gatehttp\\source\\bin\\win_debug\\CMakeFiles\\ShowIncludes\\foo.h
        
      Found prefix "注意: 包含文件:  "
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/cl.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35209 版
      版权所有(C) Microsoft Corporation。保留所有权利。
      
      CMakeCXXCompilerId.cpp
      Microsoft (R) Incremental Linker Version 14.44.35209.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
      
      /out:CMakeCXXCompilerId.exe 
      CMakeCXXCompilerId.obj 
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.obj"
      
      The CXX compiler identification is MSVC, found in:
        D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/3.31.6-msvc6/CompilerIdCXX/CMakeCXXCompilerId.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:1288 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:250 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Detecting CXX compiler /showIncludes prefix:
        main.c
        注意: 包含文件:  D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\gatehttp\\source\\bin\\win_debug\\CMakeFiles\\ShowIncludes\\foo.h
        
      Found prefix "注意: 包含文件:  "
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:5 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-7ysdgf"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-7ysdgf"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-7ysdgf'
        
        Run Build Command(s): C:/PROGRA~1/MICROS~2/2022/COMMUN~1/Common7/IDE/COMMON~1/MICROS~1/CMake/Ninja/ninja.exe -v cmTC_76989
        [1/2] C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo   /DWIN32 /D_WINDOWS /W3  /MDd /Zi /Ob0 /Od /RTC1 /showIncludes /FoCMakeFiles\\cmTC_76989.dir\\CMakeCCompilerABI.c.obj /FdCMakeFiles\\cmTC_76989.dir\\ /FS -c "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin\\cmake.exe" -E vs_link_exe --msvc-ver=1944 --intdir=CMakeFiles\\cmTC_76989.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_76989.dir\\CMakeCCompilerABI.c.obj  /out:cmTC_76989.exe /implib:cmTC_76989.lib /pdb:cmTC_76989.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/PROGRA~1/MICROS~2/2022/COMMUN~1/VC/Tools/MSVC/1444~1.352/bin/Hostx64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Running the C compiler's linker: "C:/PROGRA~1/MICROS~2/2022/COMMUN~1/VC/Tools/MSVC/1444~1.352/bin/Hostx64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35209.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:5 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-19f7j4"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-19f7j4"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-19f7j4'
        
        Run Build Command(s): C:/PROGRA~1/MICROS~2/2022/COMMUN~1/Common7/IDE/COMMON~1/MICROS~1/CMake/Ninja/ninja.exe -v cmTC_31e6d
        [1/2] C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP   /DWIN32 /D_WINDOWS /W3 /GR /EHsc  /MDd /Zi /Ob0 /Od /RTC1 /showIncludes /FoCMakeFiles\\cmTC_31e6d.dir\\CMakeCXXCompilerABI.cpp.obj /FdCMakeFiles\\cmTC_31e6d.dir\\ /FS -c "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin\\cmake.exe" -E vs_link_exe --msvc-ver=1944 --intdir=CMakeFiles\\cmTC_31e6d.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_31e6d.dir\\CMakeCXXCompilerABI.cpp.obj  /out:cmTC_31e6d.exe /implib:cmTC_31e6d.lib /pdb:cmTC_31e6d.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/PROGRA~1/MICROS~2/2022/COMMUN~1/VC/Tools/MSVC/1444~1.352/bin/Hostx64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Running the CXX compiler's linker: "C:/PROGRA~1/MICROS~2/2022/COMMUN~1/VC/Tools/MSVC/1444~1.352/bin/Hostx64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35209.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:42 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_NO_UNUSED_PARAMETER_MSVC"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-7k4how"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-7k4how"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/cmake"
    buildResult:
      variable: "LC_LINT_NO_UNUSED_PARAMETER_MSVC"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-7k4how'
        
        Run Build Command(s): C:/PROGRA~1/MICROS~2/2022/COMMUN~1/Common7/IDE/COMMON~1/MICROS~1/CMake/Ninja/ninja.exe -v cmTC_d423c
        [1/2] C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo -DLC_LINT_NO_UNUSED_PARAMETER_MSVC  /DWIN32 /D_WINDOWS /W3  /MDd /Zi /Ob0 /Od /RTC1   /wd4100 /showIncludes /FoCMakeFiles\\cmTC_d423c.dir\\src.c.obj /FdCMakeFiles\\cmTC_d423c.dir\\ /FS -c D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\gatehttp\\source\\bin\\win_debug\\CMakeFiles\\CMakeScratch\\TryCompile-7k4how\\src.c
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin\\cmake.exe" -E vs_link_exe --msvc-ver=1944 --intdir=CMakeFiles\\cmTC_d423c.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_d423c.dir\\src.c.obj  /out:cmTC_d423c.exe /implib:cmTC_d423c.lib /pdb:cmTC_d423c.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:43 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_NO_CONDITIONAL_CONSTANT_MSVC"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-w4rr9n"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-w4rr9n"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/cmake"
    buildResult:
      variable: "LC_LINT_NO_CONDITIONAL_CONSTANT_MSVC"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-w4rr9n'
        
        Run Build Command(s): C:/PROGRA~1/MICROS~2/2022/COMMUN~1/Common7/IDE/COMMON~1/MICROS~1/CMake/Ninja/ninja.exe -v cmTC_e57cb
        [1/2] C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo -DLC_LINT_NO_CONDITIONAL_CONSTANT_MSVC  /DWIN32 /D_WINDOWS /W3  /MDd /Zi /Ob0 /Od /RTC1   /wd4127 /showIncludes /FoCMakeFiles\\cmTC_e57cb.dir\\src.c.obj /FdCMakeFiles\\cmTC_e57cb.dir\\ /FS -c D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\gatehttp\\source\\bin\\win_debug\\CMakeFiles\\CMakeScratch\\TryCompile-w4rr9n\\src.c
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin\\cmake.exe" -E vs_link_exe --msvc-ver=1944 --intdir=CMakeFiles\\cmTC_e57cb.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_e57cb.dir\\src.c.obj  /out:cmTC_e57cb.exe /implib:cmTC_e57cb.lib /pdb:cmTC_e57cb.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:44 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_NO_NONSTANDARD_MSVC"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-a2ok6o"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-a2ok6o"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/cmake"
    buildResult:
      variable: "LC_LINT_NO_NONSTANDARD_MSVC"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-a2ok6o'
        
        Run Build Command(s): C:/PROGRA~1/MICROS~2/2022/COMMUN~1/Common7/IDE/COMMON~1/MICROS~1/CMake/Ninja/ninja.exe -v cmTC_badaf
        [1/2] C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo -DLC_LINT_NO_NONSTANDARD_MSVC  /DWIN32 /D_WINDOWS /W3  /MDd /Zi /Ob0 /Od /RTC1   /wd4201 /showIncludes /FoCMakeFiles\\cmTC_badaf.dir\\src.c.obj /FdCMakeFiles\\cmTC_badaf.dir\\ /FS -c D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\gatehttp\\source\\bin\\win_debug\\CMakeFiles\\CMakeScratch\\TryCompile-a2ok6o\\src.c
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin\\cmake.exe" -E vs_link_exe --msvc-ver=1944 --intdir=CMakeFiles\\cmTC_badaf.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_badaf.dir\\src.c.obj  /out:cmTC_badaf.exe /implib:cmTC_badaf.lib /pdb:cmTC_badaf.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:45 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_NO_NONSTANDARD_EMPTY_TU_MSVC"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-afjuvu"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-afjuvu"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/cmake"
    buildResult:
      variable: "LC_LINT_NO_NONSTANDARD_EMPTY_TU_MSVC"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-afjuvu'
        
        Run Build Command(s): C:/PROGRA~1/MICROS~2/2022/COMMUN~1/Common7/IDE/COMMON~1/MICROS~1/CMake/Ninja/ninja.exe -v cmTC_61b92
        [1/2] C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo -DLC_LINT_NO_NONSTANDARD_EMPTY_TU_MSVC  /DWIN32 /D_WINDOWS /W3  /MDd /Zi /Ob0 /Od /RTC1   /wd4206 /showIncludes /FoCMakeFiles\\cmTC_61b92.dir\\src.c.obj /FdCMakeFiles\\cmTC_61b92.dir\\ /FS -c D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\gatehttp\\source\\bin\\win_debug\\CMakeFiles\\CMakeScratch\\TryCompile-afjuvu\\src.c
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin\\cmake.exe" -E vs_link_exe --msvc-ver=1944 --intdir=CMakeFiles\\cmTC_61b92.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_61b92.dir\\src.c.obj  /out:cmTC_61b92.exe /implib:cmTC_61b92.lib /pdb:cmTC_61b92.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:46 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_NO_NONSTANDARD_FILE_SCOPE_MSVC"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-kvo0tx"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-kvo0tx"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/cmake"
    buildResult:
      variable: "LC_LINT_NO_NONSTANDARD_FILE_SCOPE_MSVC"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-kvo0tx'
        
        Run Build Command(s): C:/PROGRA~1/MICROS~2/2022/COMMUN~1/Common7/IDE/COMMON~1/MICROS~1/CMake/Ninja/ninja.exe -v cmTC_a250f
        [1/2] C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo -DLC_LINT_NO_NONSTANDARD_FILE_SCOPE_MSVC  /DWIN32 /D_WINDOWS /W3  /MDd /Zi /Ob0 /Od /RTC1   /wd4210 /showIncludes /FoCMakeFiles\\cmTC_a250f.dir\\src.c.obj /FdCMakeFiles\\cmTC_a250f.dir\\ /FS -c D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\gatehttp\\source\\bin\\win_debug\\CMakeFiles\\CMakeScratch\\TryCompile-kvo0tx\\src.c
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin\\cmake.exe" -E vs_link_exe --msvc-ver=1944 --intdir=CMakeFiles\\cmTC_a250f.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_a250f.dir\\src.c.obj  /out:cmTC_a250f.exe /implib:cmTC_a250f.lib /pdb:cmTC_a250f.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:47 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_NO_NONSTANDARD_NONSTATIC_DLIMPORT_MSVC"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-ixu2he"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-ixu2he"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/cmake"
    buildResult:
      variable: "LC_LINT_NO_NONSTANDARD_NONSTATIC_DLIMPORT_MSVC"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-ixu2he'
        
        Run Build Command(s): C:/PROGRA~1/MICROS~2/2022/COMMUN~1/Common7/IDE/COMMON~1/MICROS~1/CMake/Ninja/ninja.exe -v cmTC_d15b2
        [1/2] C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo -DLC_LINT_NO_NONSTANDARD_NONSTATIC_DLIMPORT_MSVC  /DWIN32 /D_WINDOWS /W3  /MDd /Zi /Ob0 /Od /RTC1   /wd4232 /showIncludes /FoCMakeFiles\\cmTC_d15b2.dir\\src.c.obj /FdCMakeFiles\\cmTC_d15b2.dir\\ /FS -c D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\gatehttp\\source\\bin\\win_debug\\CMakeFiles\\CMakeScratch\\TryCompile-ixu2he\\src.c
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin\\cmake.exe" -E vs_link_exe --msvc-ver=1944 --intdir=CMakeFiles\\cmTC_d15b2.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_d15b2.dir\\src.c.obj  /out:cmTC_d15b2.exe /implib:cmTC_d15b2.lib /pdb:cmTC_d15b2.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:48 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_NO_HIDES_LOCAL"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-sipmpp"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-sipmpp"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/cmake"
    buildResult:
      variable: "LC_LINT_NO_HIDES_LOCAL"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-sipmpp'
        
        Run Build Command(s): C:/PROGRA~1/MICROS~2/2022/COMMUN~1/Common7/IDE/COMMON~1/MICROS~1/CMake/Ninja/ninja.exe -v cmTC_6a1a0
        [1/2] C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo -DLC_LINT_NO_HIDES_LOCAL  /DWIN32 /D_WINDOWS /W3  /MDd /Zi /Ob0 /Od /RTC1   /wd4456 /showIncludes /FoCMakeFiles\\cmTC_6a1a0.dir\\src.c.obj /FdCMakeFiles\\cmTC_6a1a0.dir\\ /FS -c D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\gatehttp\\source\\bin\\win_debug\\CMakeFiles\\CMakeScratch\\TryCompile-sipmpp\\src.c
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin\\cmake.exe" -E vs_link_exe --msvc-ver=1944 --intdir=CMakeFiles\\cmTC_6a1a0.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_6a1a0.dir\\src.c.obj  /out:cmTC_6a1a0.exe /implib:cmTC_6a1a0.lib /pdb:cmTC_6a1a0.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:49 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_NO_HIDES_PARAM"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-28kv7t"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-28kv7t"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/cmake"
    buildResult:
      variable: "LC_LINT_NO_HIDES_PARAM"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-28kv7t'
        
        Run Build Command(s): C:/PROGRA~1/MICROS~2/2022/COMMUN~1/Common7/IDE/COMMON~1/MICROS~1/CMake/Ninja/ninja.exe -v cmTC_31303
        [1/2] C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo -DLC_LINT_NO_HIDES_PARAM  /DWIN32 /D_WINDOWS /W3  /MDd /Zi /Ob0 /Od /RTC1   /wd4457 /showIncludes /FoCMakeFiles\\cmTC_31303.dir\\src.c.obj /FdCMakeFiles\\cmTC_31303.dir\\ /FS -c D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\gatehttp\\source\\bin\\win_debug\\CMakeFiles\\CMakeScratch\\TryCompile-28kv7t\\src.c
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin\\cmake.exe" -E vs_link_exe --msvc-ver=1944 --intdir=CMakeFiles\\cmTC_31303.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_31303.dir\\src.c.obj  /out:cmTC_31303.exe /implib:cmTC_31303.lib /pdb:cmTC_31303.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:50 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_NO_HIDES_GLOBAL"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-kanpv7"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-kanpv7"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/cmake"
    buildResult:
      variable: "LC_LINT_NO_HIDES_GLOBAL"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-kanpv7'
        
        Run Build Command(s): C:/PROGRA~1/MICROS~2/2022/COMMUN~1/Common7/IDE/COMMON~1/MICROS~1/CMake/Ninja/ninja.exe -v cmTC_68a8f
        [1/2] C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo -DLC_LINT_NO_HIDES_GLOBAL  /DWIN32 /D_WINDOWS /W3  /MDd /Zi /Ob0 /Od /RTC1   /wd4459 /showIncludes /FoCMakeFiles\\cmTC_68a8f.dir\\src.c.obj /FdCMakeFiles\\cmTC_68a8f.dir\\ /FS -c D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\gatehttp\\source\\bin\\win_debug\\CMakeFiles\\CMakeScratch\\TryCompile-kanpv7\\src.c
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin\\cmake.exe" -E vs_link_exe --msvc-ver=1944 --intdir=CMakeFiles\\cmTC_68a8f.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_68a8f.dir\\src.c.obj  /out:cmTC_68a8f.exe /implib:cmTC_68a8f.lib /pdb:cmTC_68a8f.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:51 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_NO_CONDITIONAL_ASSIGNMENT_MSVC"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-vth4ic"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-vth4ic"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/cmake"
    buildResult:
      variable: "LC_LINT_NO_CONDITIONAL_ASSIGNMENT_MSVC"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-vth4ic'
        
        Run Build Command(s): C:/PROGRA~1/MICROS~2/2022/COMMUN~1/Common7/IDE/COMMON~1/MICROS~1/CMake/Ninja/ninja.exe -v cmTC_fc301
        [1/2] C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo -DLC_LINT_NO_CONDITIONAL_ASSIGNMENT_MSVC  /DWIN32 /D_WINDOWS /W3  /MDd /Zi /Ob0 /Od /RTC1   /wd4706 /showIncludes /FoCMakeFiles\\cmTC_fc301.dir\\src.c.obj /FdCMakeFiles\\cmTC_fc301.dir\\ /FS -c D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\gatehttp\\source\\bin\\win_debug\\CMakeFiles\\CMakeScratch\\TryCompile-vth4ic\\src.c
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin\\cmake.exe" -E vs_link_exe --msvc-ver=1944 --intdir=CMakeFiles\\cmTC_fc301.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_fc301.dir\\src.c.obj  /out:cmTC_fc301.exe /implib:cmTC_fc301.lib /pdb:cmTC_fc301.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:52 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_NO_UNSAFE_MSVC"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-z13kiq"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-z13kiq"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/cmake"
    buildResult:
      variable: "LC_LINT_NO_UNSAFE_MSVC"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-z13kiq'
        
        Run Build Command(s): C:/PROGRA~1/MICROS~2/2022/COMMUN~1/Common7/IDE/COMMON~1/MICROS~1/CMake/Ninja/ninja.exe -v cmTC_c7230
        [1/2] C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo -DLC_LINT_NO_UNSAFE_MSVC  /DWIN32 /D_WINDOWS /W3  /MDd /Zi /Ob0 /Od /RTC1   /wd4996 /showIncludes /FoCMakeFiles\\cmTC_c7230.dir\\src.c.obj /FdCMakeFiles\\cmTC_c7230.dir\\ /FS -c D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\gatehttp\\source\\bin\\win_debug\\CMakeFiles\\CMakeScratch\\TryCompile-z13kiq\\src.c
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin\\cmake.exe" -E vs_link_exe --msvc-ver=1944 --intdir=CMakeFiles\\cmTC_c7230.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_c7230.dir\\src.c.obj  /out:cmTC_c7230.exe /implib:cmTC_c7230.lib /pdb:cmTC_c7230.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:54 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_WALL"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-wp32bh"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-wp32bh"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/cmake"
    buildResult:
      variable: "LC_LINT_WALL"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-wp32bh'
        
        Run Build Command(s): C:/PROGRA~1/MICROS~2/2022/COMMUN~1/Common7/IDE/COMMON~1/MICROS~1/CMake/Ninja/ninja.exe -v cmTC_0d2a2
        [1/2] C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo -DLC_LINT_WALL  /DWIN32 /D_WINDOWS /W3  /MDd /Zi /Ob0 /Od /RTC1   -Wall /showIncludes /FoCMakeFiles\\cmTC_0d2a2.dir\\src.c.obj /FdCMakeFiles\\cmTC_0d2a2.dir\\ /FS -c D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\gatehttp\\source\\bin\\win_debug\\CMakeFiles\\CMakeScratch\\TryCompile-wp32bh\\src.c
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin\\cmake.exe" -E vs_link_exe --msvc-ver=1944 --intdir=CMakeFiles\\cmTC_0d2a2.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_0d2a2.dir\\src.c.obj  /out:cmTC_0d2a2.exe /implib:cmTC_0d2a2.lib /pdb:cmTC_0d2a2.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:56 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_NO_UNUSED_PARAMETER"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-twcu8p"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-twcu8p"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/cmake"
    buildResult:
      variable: "LC_LINT_NO_UNUSED_PARAMETER"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-twcu8p'
        
        Run Build Command(s): C:/PROGRA~1/MICROS~2/2022/COMMUN~1/Common7/IDE/COMMON~1/MICROS~1/CMake/Ninja/ninja.exe -v cmTC_81c3b
        [1/2] C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo -DLC_LINT_NO_UNUSED_PARAMETER  /DWIN32 /D_WINDOWS /W3  /MDd /Zi /Ob0 /Od /RTC1   -Wno-unused-parameter /showIncludes /FoCMakeFiles\\cmTC_81c3b.dir\\src.c.obj /FdCMakeFiles\\cmTC_81c3b.dir\\ /FS -c D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\gatehttp\\source\\bin\\win_debug\\CMakeFiles\\CMakeScratch\\TryCompile-twcu8p\\src.c
        FAILED: CMakeFiles/cmTC_81c3b.dir/src.c.obj 
        C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo -DLC_LINT_NO_UNUSED_PARAMETER  /DWIN32 /D_WINDOWS /W3  /MDd /Zi /Ob0 /Od /RTC1   -Wno-unused-parameter /showIncludes /FoCMakeFiles\\cmTC_81c3b.dir\\src.c.obj /FdCMakeFiles\\cmTC_81c3b.dir\\ /FS -c D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\gatehttp\\source\\bin\\win_debug\\CMakeFiles\\CMakeScratch\\TryCompile-twcu8p\\src.c
        cl: 命令行 error D8021 :无效的数值参数“/Wno-unused-parameter”
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:57 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_STRICT_PROTOTYPES"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-cj5nl8"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-cj5nl8"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/cmake"
    buildResult:
      variable: "LC_LINT_STRICT_PROTOTYPES"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-cj5nl8'
        
        Run Build Command(s): C:/PROGRA~1/MICROS~2/2022/COMMUN~1/Common7/IDE/COMMON~1/MICROS~1/CMake/Ninja/ninja.exe -v cmTC_44b6e
        [1/2] C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo -DLC_LINT_STRICT_PROTOTYPES  /DWIN32 /D_WINDOWS /W3  /MDd /Zi /Ob0 /Od /RTC1   -Wstrict-prototypes /showIncludes /FoCMakeFiles\\cmTC_44b6e.dir\\src.c.obj /FdCMakeFiles\\cmTC_44b6e.dir\\ /FS -c D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\gatehttp\\source\\bin\\win_debug\\CMakeFiles\\CMakeScratch\\TryCompile-cj5nl8\\src.c
        FAILED: CMakeFiles/cmTC_44b6e.dir/src.c.obj 
        C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo -DLC_LINT_STRICT_PROTOTYPES  /DWIN32 /D_WINDOWS /W3  /MDd /Zi /Ob0 /Od /RTC1   -Wstrict-prototypes /showIncludes /FoCMakeFiles\\cmTC_44b6e.dir\\src.c.obj /FdCMakeFiles\\cmTC_44b6e.dir\\ /FS -c D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\gatehttp\\source\\bin\\win_debug\\CMakeFiles\\CMakeScratch\\TryCompile-cj5nl8\\src.c
        cl: 命令行 error D8021 :无效的数值参数“/Wstrict-prototypes”
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:58 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_EXTRA"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-vz6vrc"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-vz6vrc"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/cmake"
    buildResult:
      variable: "LC_LINT_EXTRA"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-vz6vrc'
        
        Run Build Command(s): C:/PROGRA~1/MICROS~2/2022/COMMUN~1/Common7/IDE/COMMON~1/MICROS~1/CMake/Ninja/ninja.exe -v cmTC_89b96
        [1/2] C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo -DLC_LINT_EXTRA  /DWIN32 /D_WINDOWS /W3  /MDd /Zi /Ob0 /Od /RTC1   -Wextra /showIncludes /FoCMakeFiles\\cmTC_89b96.dir\\src.c.obj /FdCMakeFiles\\cmTC_89b96.dir\\ /FS -c D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\gatehttp\\source\\bin\\win_debug\\CMakeFiles\\CMakeScratch\\TryCompile-vz6vrc\\src.c
        FAILED: CMakeFiles/cmTC_89b96.dir/src.c.obj 
        C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo -DLC_LINT_EXTRA  /DWIN32 /D_WINDOWS /W3  /MDd /Zi /Ob0 /Od /RTC1   -Wextra /showIncludes /FoCMakeFiles\\cmTC_89b96.dir\\src.c.obj /FdCMakeFiles\\cmTC_89b96.dir\\ /FS -c D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\gatehttp\\source\\bin\\win_debug\\CMakeFiles\\CMakeScratch\\TryCompile-vz6vrc\\src.c
        cl: 命令行 error D8021 :无效的数值参数“/Wextra”
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:60 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_BUILTIN_REDEFINE"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-ksust1"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-ksust1"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/cmake"
    buildResult:
      variable: "LC_LINT_BUILTIN_REDEFINE"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-ksust1'
        
        Run Build Command(s): C:/PROGRA~1/MICROS~2/2022/COMMUN~1/Common7/IDE/COMMON~1/MICROS~1/CMake/Ninja/ninja.exe -v cmTC_a8bd1
        [1/2] C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo -DLC_LINT_BUILTIN_REDEFINE  /DWIN32 /D_WINDOWS /W3  /MDd /Zi /Ob0 /Od /RTC1   -Wno-builtin-macro-redefined /showIncludes /FoCMakeFiles\\cmTC_a8bd1.dir\\src.c.obj /FdCMakeFiles\\cmTC_a8bd1.dir\\ /FS -c D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\gatehttp\\source\\bin\\win_debug\\CMakeFiles\\CMakeScratch\\TryCompile-ksust1\\src.c
        FAILED: CMakeFiles/cmTC_a8bd1.dir/src.c.obj 
        C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo -DLC_LINT_BUILTIN_REDEFINE  /DWIN32 /D_WINDOWS /W3  /MDd /Zi /Ob0 /Od /RTC1   -Wno-builtin-macro-redefined /showIncludes /FoCMakeFiles\\cmTC_a8bd1.dir\\src.c.obj /FdCMakeFiles\\cmTC_a8bd1.dir\\ /FS -c D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\gatehttp\\source\\bin\\win_debug\\CMakeFiles\\CMakeScratch\\TryCompile-ksust1\\src.c
        cl: 命令行 error D8021 :无效的数值参数“/Wno-builtin-macro-redefined”
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:63 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_UTF8_MSVC"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-4udgd3"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-4udgd3"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/cmake"
    buildResult:
      variable: "LC_LINT_UTF8_MSVC"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-4udgd3'
        
        Run Build Command(s): C:/PROGRA~1/MICROS~2/2022/COMMUN~1/Common7/IDE/COMMON~1/MICROS~1/CMake/Ninja/ninja.exe -v cmTC_01b3e
        [1/2] C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo -DLC_LINT_UTF8_MSVC  /DWIN32 /D_WINDOWS /W3  /MDd /Zi /Ob0 /Od /RTC1   /utf-8 /showIncludes /FoCMakeFiles\\cmTC_01b3e.dir\\src.c.obj /FdCMakeFiles\\cmTC_01b3e.dir\\ /FS -c D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\gatehttp\\source\\bin\\win_debug\\CMakeFiles\\CMakeScratch\\TryCompile-4udgd3\\src.c
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin\\cmake.exe" -E vs_link_exe --msvc-ver=1944 --intdir=CMakeFiles\\cmTC_01b3e.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_01b3e.dir\\src.c.obj  /out:cmTC_01b3e.exe /implib:cmTC_01b3e.lib /pdb:cmTC_01b3e.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        FAILED: cmTC_01b3e.exe 
        C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin\\cmake.exe" -E vs_link_exe --msvc-ver=1944 --intdir=CMakeFiles\\cmTC_01b3e.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_01b3e.dir\\src.c.obj  /out:cmTC_01b3e.exe /implib:cmTC_01b3e.lib /pdb:cmTC_01b3e.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        FINAL LINK: command "C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_01b3e.dir\\src.c.obj /out:cmTC_01b3e.exe /implib:cmTC_01b3e.lib /pdb:cmTC_01b3e.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTFILE:CMakeFiles\\cmTC_01b3e.dir/intermediate.manifest CMakeFiles\\cmTC_01b3e.dir/manifest.res" failed (exit code 1104) with the following output:
        LINK : fatal error LNK1104: 无法打开文件“cmTC_01b3e.exe”\x0d
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:109 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_F_STRICT_ALIASING"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-uh28qy"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-uh28qy"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/cmake"
    buildResult:
      variable: "LC_F_STRICT_ALIASING"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/gatehttp/source/bin/win_debug/CMakeFiles/CMakeScratch/TryCompile-uh28qy'
        
        Run Build Command(s): C:/PROGRA~1/MICROS~2/2022/COMMUN~1/Common7/IDE/COMMON~1/MICROS~1/CMake/Ninja/ninja.exe -v cmTC_c461b
        [1/2] C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo -DLC_F_STRICT_ALIASING  /DWIN32 /D_WINDOWS /W3  /MDd /Zi /Ob0 /Od /RTC1   -fno-strict-aliasing /showIncludes /FoCMakeFiles\\cmTC_c461b.dir\\src.c.obj /FdCMakeFiles\\cmTC_c461b.dir\\ /FS -c D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\gatehttp\\source\\bin\\win_debug\\CMakeFiles\\CMakeScratch\\TryCompile-uh28qy\\src.c
        cl: 命令行 warning D9002 :忽略未知选项“-fno-strict-aliasing”
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\bin\\cmake.exe" -E vs_link_exe --msvc-ver=1944 --intdir=CMakeFiles\\cmTC_c461b.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~2\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_c461b.dir\\src.c.obj  /out:cmTC_c461b.exe /implib:cmTC_c461b.lib /pdb:cmTC_c461b.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...
