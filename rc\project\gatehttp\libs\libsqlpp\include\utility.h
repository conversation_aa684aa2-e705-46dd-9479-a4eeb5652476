#pragma once
#include <string>
#include <sstream>
#if defined __GNUC__
#define sql_print_t vsnprintf
#define SQL_CHECK_PRINTF(m,n) __attribute__((format(printf,m,n)))
#define NO_RETURN	__attribute__((noreturn))
#else
#define SQL_CHECK_PRINTF(m,n)
#define NO_RETURN
#define sql_print_t vsnprintf
#endif
namespace sql {
	void str_to_lwr(std::string& s);
	void str_to_lwr(std::string& ls, const char* mcs);

	template <class T>
	std::string stream_to_string(const T& object) {
		std::ostringstream str;
		str << object;
		return str.str();
	}

	std::string str_format(const char* fmt, ...) SQL_CHECK_PRINTF(1, 2);

	bool str_contains(const std::string & str, const std::string & key);
}
